import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_confirmed/start_return/start_return_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_confirmed/start_return/return_by_logistics/return_by_logistics.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_confirmed/start_return/return_by_seller/return_by_seller.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_customer_details/seller_all_order_customer_details_bloc.dart';

class StartReturn extends StatefulWidget {
  final List<SubOrder> suborderList;
  final SellerSubOrderBloc sellerSubOrderBloc;
  final Order order;

  const StartReturn({
    Key? key,
    required this.suborderList,
    required this.sellerSubOrderBloc,
    required this.order,
  }) : super(key: key);

  @override
  State<StartReturn> createState() => _StartReturnState();
}

class _StartReturnState extends State<StartReturn> {
  //region Bloc
  late StartReturnBloc startReturnBloc;
  late SellerAllOrderCustomerDetailBloc customerDetailBloc;

  //endregion

  //region Init
  @override
  void initState() {
    super.initState();
    startReturnBloc = StartReturnBloc(
      context: context,
      sellerSubOrderBloc: widget.sellerSubOrderBloc,
      order: widget.order,
      suborderList: widget.suborderList,
    );
    startReturnBloc.init();

    customerDetailBloc =
        SellerAllOrderCustomerDetailBloc(context, widget.order);
    customerDetailBloc.init();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: startReturnBloc.logisticAndLinkCtrl.stream,
        builder: (context, snapshot) {
          return GestureDetector(
              onTap: () {
                CommonMethods.closeKeyboard(context);
              },
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        selectUnselect(),
                      ],
                    ),
                  ),
                  StreamBuilder<StartReturnState>(
                      stream: startReturnBloc.startShippingCtrl.stream,
                      builder: (context, snapshot) {
                        if (snapshot.data == StartReturnState.Loading) {
                          return const InkWell(
                              child: SizedBox(
                            height: double.infinity,
                            width: double.infinity,
                          ));
                        }
                        return const SizedBox();
                      })
                ],
              ));
        });
  }

  //region Select unselect
  Widget selectUnselect() {
    return Column(
      children: [
        ///Sub title
        Container(
            margin: const EdgeInsets.all(10),
            child: SellerAllOrdersCommonWidgets.sellerBottomSheetSubTitle(
                title: "You can group returns for easier processing")),

        ///Drop down
        InkWell(
          onTap: () {
            startReturnBloc.selectUnSelect();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 6.5),
            decoration: BoxDecoration(
                color: AppColors.lightestGrey2,
                borderRadius: BorderRadius.circular(7)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Drop down
                Container(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      Text(
                        AppStrings.selectUnSelect,
                        style: AppTextStyle.heading3Bold(
                            textColor: AppColors.appBlack),
                      ),
                      Expanded(child: horizontalSizedBox(10)),
                      startReturnBloc.isSelectUnselectVisible
                          ? RotatedBox(
                              quarterTurns: 3,
                              child: RotatedBox(
                                  quarterTurns: 4,
                                  child: SvgPicture.asset(AppImages.arrow3)),
                            )
                          : RotatedBox(
                              quarterTurns: 1,
                              child: SvgPicture.asset(AppImages.arrow3))
                    ],
                  ),
                ),

                ///List of product
                Visibility(
                    visible: startReturnBloc.isSelectUnselectVisible,
                    child: subOrderList()),

                ///Selected product count
                Visibility(
                  visible: !startReturnBloc.isSelectUnselectVisible,
                  child: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: Text(
                        "${CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList) == 0 ? "No" : CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList)} ${CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList) == 1 ? "suborder" : "suborders"} selected",
                        style: AppTextStyle.settingText(
                            textColor: AppColors.appBlack),
                      )),
                )
              ],
            ),
          ),
        ),

        verticalSizedBox(10),

        Visibility(
          visible: !startReturnBloc.isGrouped,
          child: Padding(
            padding: const EdgeInsets.only(left: 10, right: 10, bottom: 20),
            child: deliveryDate(),
          ),
        ),

        ///Confirm and cancel
        Visibility(
            visible: !startReturnBloc.isShippingDetailVisible,
            child: groupAndCancel()),

        // ///Add return details
        // Container(
        //   padding: const EdgeInsets.all(10),
        //   margin: const EdgeInsets.all(10),
        //   child: Text("Add Return Details",
        //       maxLines: 2,
        //       style: AppTextStyle.access1(textColor: AppColors.appBlack)),
        // ),

        ///Add return details
        Visibility(
            visible: startReturnBloc.isShippingDetailVisible,
            child: addShippingAndTrackingDetail()),

        ///Space
        AppCommonWidgets.bottomListSpace(context: context),
      ],
    );
  }

  //endregion

//region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.suborderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(
                  subOrder: widget.suborderList[index],
                  onTap: () {
                    //Make is select to true
                    startReturnBloc.onSelectSubOrder(
                        subOrder: widget.suborderList[index]);

                    //Date time
                    startReturnBloc.onSelectChangeDate();
                    //If grouped then call
                    if (startReturnBloc.isGrouped) {
                      //Show group button
                      startReturnBloc.isShippingDetailVisible = false;
                      startReturnBloc.logisticAndLinkCtrl.sink.add(true);
                    }
                  },
                  context: context),
              index == widget.suborderList.length - 1
                  ? const SizedBox()
                  : const Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }

//endregion

//region Group and cancel
  Widget groupAndCancel() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Expanded(
            child: AppCommonWidgets.activeButton(
                buttonName: CommonMethods.returnHowManySubOrdersSelected(
                            subOrderList: widget.suborderList) ==
                        1
                    ? "Process Return"
                    : "Group Returns",
                onTap: () {
                  startReturnBloc.groupSubOrders(
                      selectedSubOrders:
                          CommonMethods.sellerSelectedSubOrderNumberList(
                              widget.suborderList),
                      subOrderList: widget.suborderList);
                }),
          ),
          horizontalSizedBox(10),
          Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: CommonMethods.isAllOrderSelectedSelected(
                        subOrderList: widget.suborderList)
                    ? AppStrings.cancelAll
                    : AppStrings.cancel,
                onTap: () {
                  startReturnBloc.onTapCancel(
                      subOrderNumbers:
                          CommonMethods.sellerSelectedSubOrderNumberList(
                              widget.suborderList));
                }),
          )
        ],
      ),
    );
  }

//endregion

//region Add shipping and tracking detail
  Widget addShippingAndTrackingDetail() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: StreamBuilder<bool>(
          stream: startReturnBloc.logisticAndLinkCtrl.stream,
          builder: (context, snapshot) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("Add Return Details",
              maxLines: 2,
              style: AppTextStyle.access1(textColor: AppColors.appBlack)),
                ///Group name
                AppTitleAndOptions(
                  title: AppStrings.groupName,
                  option: AppTextFields.allTextField(
                    context: context,
                    maxEntry: 50,
                    onChanged: () {
                      startReturnBloc.onChangeGroupName();
                    },
                    textEditingController: startReturnBloc.packageNameTextCtr,
                    hintText: AppStrings.pkg,
                  ),
                ),
                verticalSizedBox(20),

                ///Estimate delivery
                deliveryDate(),
                verticalSizedBox(20),

                ///Select delivery method
                selectDeliveryMethod(),
              ],
            );
          }),
    );
  }

//endregion

//region Delivery date
  Widget deliveryDate() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: "Estimated Return Pickup Date",
          option: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  startReturnBloc.estimatedReturnDate,
                  style:
                      AppTextStyle.settingText(textColor: AppColors.appBlack),
                ),
                horizontalSizedBox(30),
                InkWell(
                  onTap: () {
                    startReturnBloc.onTapCalender();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: const BoxDecoration(
                        color: AppColors.textFieldFill1,
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          AppImages.calender,
                          color: AppColors.appBlack,
                        ),
                        horizontalSizedBox(10),
                        Text(
                          "Update Estimated return Date",
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

//endregion

//region Select product delivery method
  Widget selectDeliveryMethod() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Select product delivery method

        AppTitleAndOptions(
          title: "Select Return Method",
          option: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///Self
              appRadioCheckBox(
                  isActive: startReturnBloc.isSelfDelivery,
                  text: "Self-return by Store",
                  isExpand: false,
                  onTap: () {
                    startReturnBloc.onSelectDeliveryMethod(true);
                  }),

              ///Return by logistics partner
              appRadioCheckBox(
                  isActive: !startReturnBloc.isSelfDelivery &&
                      !startReturnBloc.isSwadesicShipping,
                  text: "Return by logistics partner",
                  isExpand: false,
                  onTap: () {
                    startReturnBloc.onSelectDeliveryMethod(false);
                    startReturnBloc.setSwadesicShipping(false);
                  }),

              ///Swadesic Shipping (disabled)
              appRadioCheckBox(
                  isActive: false,
                  text: "Swadesic shipping (not available for returns)",
                  isExpand: false,
                  onTap: () {
                    // Disabled for returns
                  }),
            ],
          ),
        ),
        verticalSizedBox(20),

        ///Self delivery
        Visibility(
            visible: startReturnBloc.isSelfDelivery, child: selfDelivery()),

        ///Delivery by logistic
        Visibility(
            visible: !startReturnBloc.isSelfDelivery &&
                !startReturnBloc.isSwadesicShipping,
            child: deliveryByLogistic()),

        ///Swadesic shipping section
        Visibility(
            visible: startReturnBloc.isSwadesicShipping,
            child: deliveryBySwadesic()),

        verticalSizedBox(20),

        ///Mark as shipped
        Visibility(
            visible: startReturnBloc.isShippingDetailVisible,
            child: markAsShipped())
      ],
    );
  }

//endregion

//region Self return
  Widget selfDelivery() {
    return ReturnBySeller(startReturnBloc: startReturnBloc);
  }

//endregion

  //region Return by logistic
  Widget deliveryByLogistic() {
    return ReturnByLogistics(startReturnBloc: startReturnBloc);
  }

  //endregion

  //region Delivery by swadesic (disabled)
  Widget deliveryBySwadesic() {
    // Swadesic shipping is disabled for returns
    return Container(
      padding: const EdgeInsets.all(16),
      child: Text(
        "Swadesic shipping is not available for returns",
        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      ),
    );
  }
  //endregion

//region Initiate return button
  Widget markAsShipped() {
    return Column(
      children: [
        ///Additional notes

        AppTitleAndOptions(
          title: "Additional Return Notes",
          option: AppTextFields.allTextField(
            maxEntry: 500,
            maxLines: 5,
            minLines: 5,
            context: context,
            textEditingController: startReturnBloc.notesTextCtrl,
            hintText: "Add any additional notes for the return",
          ),
        ),
        verticalSizedBox(20),

        Text(
          "Initiating return will update the return status to in progress",
          style: AppTextStyle.subTitle(textColor: AppColors.writingBlack1),
        ),

        verticalSizedBox(10),

        Row(
          children: [
            Expanded(
              child: StreamBuilder<StartReturnState>(
                  stream: startReturnBloc.markAsShippingCtrl.stream,
                  initialData: StartReturnState.Success,
                  builder: (context, snapshot) {
                    //If Loading
                    if (snapshot.data == StartReturnState.Loading) {
                      return AppCommonWidgets.appCircularProgress();
                    }
                    return AppCommonWidgets.activeButton(
                        buttonColor: startReturnBloc.isGroupNameUnique
                            ? AppColors.brandBlack
                            : AppColors.tertiaryGreen,
                        buttonName: "Initiate Return",
                        onTap: () {
                          startReturnBloc.changeTheStatus();
                        });
                  }),
            ),
          ],
        ),
      ],
    );
  }
//endregion
}
