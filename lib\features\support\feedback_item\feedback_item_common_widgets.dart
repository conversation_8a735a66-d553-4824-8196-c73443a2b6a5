import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/support/feedback_item/feedback_item_bloc.dart';
import 'package:swadesic/model/support/get_all_feedback_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class FeedbackItemCommonWidgets {
  //region Title
  static Widget title({required String title}) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
        )
      ],
    );
  }

  //endregion
  //region Submit button
  static Widget subMitButton({required String buttonName, required onPressed}) {
    return InkWell(
      onTap: () {
        onPressed();
      },
      child: Row(
        children: [
          Expanded(
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColors.brandBlack),
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: appText(buttonName,
                  color: AppColors.appWhite,
                  fontSize: 15,
                  fontFamily: AppConstants.rRegular,
                  fontWeight: FontWeight.w700),
            ),
          ),
        ],
      ),
    );
  }

//endregion

  //region Root Feed back item card
  static Widget rootFeedbackItemCard(
      {required BuildContext context,
      required FeedbackDetail feedbackDetail,
      required onTapVote,
      required bool isAdmin,
      required FeedbackItemBloc feedbackItemBloc}) {
    return Container(
      padding: const EdgeInsets.only(top: 5, left: 10, right: 10),
      margin: const EdgeInsets.only(left: 10, right: 10, bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Feedback date time and status
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //Feedback id and date time
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Ticket Id: #${feedbackDetail.feedbackId}",
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack),
                  ),
                  verticalSizedBox(3),
                  Text(
                    "${CommonMethods.dateTimeAmPm(date: feedbackDetail.date!)[1]} ${CommonMethods.dateTimeAmPm(date: feedbackDetail.date!)[2]}",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack1),
                  )
                ],
              ),
              Expanded(child: horizontalSizedBox(10)),
              //Status
              // Container(
              //   // height: 24,
              //   alignment: Alignment.center,
              //   padding:
              //       const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
              //   decoration: BoxDecoration(
              //     // color:AppColors.brandGreen.withOpacity(0.2) ,
              //     border: Border.all(color: AppColors.borderColor1),
              //     borderRadius: BorderRadius.circular(20),
              //   ),
              //   child: isAdmin
              //       ? SizedBox(
              //           height: 20,
              //           child: DropdownButton<String>(
              //             iconSize: 10,
              //             icon: RotatedBox(
              //                 quarterTurns: 1,
              //                 child: SvgPicture.asset(
              //                   AppImages.arrow3,
              //                 )),
              //             isExpanded: false,
              //             focusColor: Colors.green,
              //             alignment: Alignment.centerRight,
              //             underline: const SizedBox(),
              //             value: feedbackDetail.status,
              //             borderRadius: BorderRadius.circular(10),
              //             padding: EdgeInsets.zero,
              //             items: feedbackItemBloc.feedbackStatusList
              //                 .map<DropdownMenuItem<String>>((String value) {
              //               return DropdownMenuItem(
              //                   alignment: Alignment.centerLeft,
              //                   value: value,
              //                   child: Text(
              //                     value,
              //                     style: AppTextStyle.subTitle(
              //                         textColor: AppColors.appBlack),
              //                   ));
              //             }).toList(),
              //             onChanged: (value) {
              //               feedbackItemBloc.onChangeDropDown(
              //                   feedbackDetail: feedbackDetail, value: value!);
              //               //shoppingCartBloc.onChangeQuantity(products: products, quantity: value!);
              //             },
              //           ),
              //         )
              //       : Text(
              //           feedbackDetail.status!,
              //           style: AppTextStyle.subTitle(
              //               textColor: AppColors.appBlack),
              //         ),
              // )
            ],
          ),
          verticalSizedBox(10),

          ///Title
          Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: SelectableText(
                  "${feedbackDetail.brief}",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
                // child: Text(
                //   "${feedbackDetail.brief}",
                //   maxLines: 3,
                //   style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                // ),
              ),
            ],
          ),
          verticalSizedBox(10),

          ///Category
          Text(
            "Category: ${feedbackDetail.screenCategory ?? 'None'}",
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          ),
          verticalSizedBox(10),

          ///Feedback type and vote
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ///report or suggestion
              Container(
                alignment: Alignment.center,
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                decoration: BoxDecoration(
                  color: feedbackDetail.feedbackType == "REPORT"
                        ? AppColors.red.withOpacity(0.2)
                        : feedbackDetail.feedbackType == "ESCALATION"
                            ? AppColors.orange.withOpacity(0.2)
                            : AppColors.darkPurple.withOpacity(0.2),
                  border: Border.all(
                      color: feedbackDetail.feedbackType == "REPORT"
                          ? AppColors.red
                          : feedbackDetail.feedbackType == "ESCALATION"
                              ? AppColors.orange
                              : AppColors.darkPurple),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  feedbackDetail.feedbackType!,
                  style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                ),
              ),

              ///Vote arrow
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                width: 30,
                height: 30,
                child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      onTapVote();
                    },
                    child: SvgPicture.asset(

                        ///If user view and also contains user ref in voted list
                        AppConstants.appData.isUserView! &&
                                feedbackDetail.votedUserList!.contains(
                                    AppConstants.appData.userReference)
                            ? AppImages.arrowActive
                            :

                            ///If store view and store ref contains
                            AppConstants.appData.isStoreView! &&
                                    feedbackDetail.votedUserList!.contains(
                                        AppConstants.appData.storeReference)
                                ? AppImages.arrowActive
                                : AppImages.arrowInActive

                        //   feedbackDetail.votedUserList!.contains
                        // (BuyerHomeBloc.userDetailsResponse.userDetail!.userReference!)
                        //   ?AppImages.arrowActive:AppImages.arrowInActive
                        )),
              ),

              ///Support count
              Text(
                feedbackDetail.upvoteCount.toString(),
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            ],
          ),

          ///Report,suggestion,support,create and approve
          // Row(
          //   children: [
          //     //report or suggestion
          //     Container(
          //       alignment: Alignment.center,
          //       width: 100,
          //       padding: const EdgeInsets.symmetric(vertical: 5),
          //       decoration: BoxDecoration(
          //         color:feedbackDetail.feedbackType=="REPORT"?AppColors.red.withOpacity(0.2):AppColors.darkPurple.withOpacity(0.2) ,
          //         border: Border.all(color: feedbackDetail.feedbackType== "REPORT"?AppColors.red:AppColors.darkPurple),
          //         borderRadius: BorderRadius.circular(20),
          //       ),
          //       child: appText(feedbackDetail.feedbackType!,fontWeight:FontWeight.w400,fontSize:12,fontFamily:AppConstants.rRegular,
          //         maxLine: 1,
          //         color: AppColors.writingColor2,
          //       ) ,
          //     ),
          //     ///Vote Arrow and status
          //     Container(
          //       margin: const EdgeInsets.symmetric(horizontal: 10),
          //       width: 30,
          //       height: 30,
          //       child: CupertinoButton(
          //           padding: EdgeInsets.zero,
          //           onPressed: (){
          //             onTapVote();
          //           },
          //           child: SvgPicture.asset(feedbackDetail.votedUserList!.contains(BuyerHomeBloc.userDetailsResponse.userDetail!.userReference!)?AppImages.arrowActive:AppImages.arrowInActive)),
          //     ),
          //
          //     ///Support count
          //     appText(feedbackDetail.upvoteCount.toString(),fontWeight:FontWeight.w400,fontSize:14,fontFamily:AppConstants.rRegular,
          //       maxLine: 1,
          //       color: AppColors.appBlack,
          //     ) ,
          //     Expanded(child: horizontalSizedBox(10)),
          //     ///Created and approved
          //     isAdmin?
          //     Container(
          //       height: 25,
          //       width: 108,
          //       // padding: EdgeInsets.only(left: 20,right: 5),
          //       alignment:Alignment.center,
          //       decoration: BoxDecoration(
          //         color: AppColors.brandGreen.withOpacity(0.2),
          //         border: Border.all(color:AppColors.brandGreen),
          //         borderRadius: BorderRadius.circular(30)
          //       ),
          //       child: DropdownButton<String>(
          //         isExpanded: false,
          //         focusColor: Colors.green,
          //         alignment: Alignment.centerRight,
          //         underline: const SizedBox(),
          //         value: feedbackDetail.status,
          //         borderRadius: BorderRadius.circular(10),
          //
          //         items: feedbackItemBloc.feedbackStatusList.map<DropdownMenuItem<String>>((String value) {
          //           return DropdownMenuItem(
          //             alignment: Alignment.centerLeft,
          //               value: value,
          //               child: appText(value,fontWeight:FontWeight.w400,fontSize:12,fontFamily:AppConstants.rRegular,
          //                 maxLine: 1,
          //                 color: AppColors.writingColor2,
          //               ));
          //         }).toList(),
          //         onChanged: (value) {
          //           feedbackItemBloc.onChangeDropDown(feedbackDetail: feedbackDetail,value: value!);
          //           //shoppingCartBloc.onChangeQuantity(products: products, quantity: value!);
          //         },
          //       ),
          //     )
          //         :Container(
          //       alignment: Alignment.center,
          //       width: 100,
          //       padding: const EdgeInsets.symmetric(vertical: 5),
          //       decoration: BoxDecoration(
          //         color:AppColors.brandGreen.withOpacity(0.2) ,
          //         border: Border.all(color:AppColors.brandGreen),
          //         borderRadius: BorderRadius.circular(20),
          //       ),
          //       child: appText(feedbackDetail.status!,fontWeight:FontWeight.w400,fontSize:12,fontFamily:AppConstants.rRegular,
          //         maxLine: 1,
          //         color: AppColors.writingColor2,
          //       ) ,
          //     ),
          //
          //   ],
          // ),
          ///Reported by and team
          ///Todo un-comment
          // Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 10),
          //   child: Row(
          //     mainAxisSize: MainAxisSize.max,
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       appText("Reported by: ${feedbackDetail.name}",fontWeight:FontWeight.w400,fontSize:12,fontFamily:AppConstants.rRegular,
          //         maxLine: 1,
          //         color: AppColors.appBlack,
          //       ),
          //       appText("Reported by: ${feedbackDetail.group}",fontWeight:FontWeight.w400,fontSize:12,fontFamily:AppConstants.rRegular,
          //         maxLine: 1,
          //         color: AppColors.appBlack,
          //       )
          //
          //     ],
          //   ),
          // ),
          ///Category
          // Row(
          //   children: [
          //     Container(
          //       margin: const EdgeInsets.symmetric(vertical:10),
          //       // width: 130,
          //       alignment: Alignment.center,
          //       padding: const EdgeInsets.symmetric(vertical:10,horizontal: 10),
          //       decoration: BoxDecoration(
          //           borderRadius: BorderRadius.circular(30),
          //           color: AppColors.lightestGrey
          //       ),
          //       child: appText("${feedbackDetail.screenCategory}",fontWeight:FontWeight.w600,fontSize:12,fontFamily:AppConstants.rRegular,
          //         maxLine: 1,
          //         color: AppColors.writingColor2,
          //       ),
          //     ),
          //   ],
          // ),

          verticalSizedBox(10),

          ///Reported by and Description
          Text(
            "Reported by: ${feedbackDetail.name}",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(10),
          SelectableText("${feedbackDetail.details}",
              style:
                  AppTextStyle.heading3Regular(textColor: AppColors.appBlack)),

          verticalSizedBox(10),

          ///Image and file
          feedbackDetail.feedbackFiles!.isEmpty
              ? const SizedBox()
              : imageAndFile(
                  feedbackDetail: feedbackDetail,
                  showTitle: true,
                  feedbackItemBloc: feedbackItemBloc,
                  context: context),
          verticalSizedBox(20),

          ///Divider
          divider(),
        ],
      ),
    );
  }

//endregion

  //region Feed back replay item card
  static Widget replayFeedbackItemCard(
      {required BuildContext context,
      required FeedbackDetail feedbackDetail,
      required FeedbackItemBloc feedbackItemBloc}) {
    return Container(
      padding: const EdgeInsets.only(top: 5, left: 10, right: 10),
      margin: const EdgeInsets.only(bottom: 25),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Other user date time
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //Name
              Text(
                "${feedbackDetail.name}",
                style: AppTextStyle.heading3Bold(textColor: AppColors.appBlack),
              ),
              Expanded(child: horizontalSizedBox(10)),
              //Date time
              Text(
                "${CommonMethods.dateTimeAmPm(date: feedbackDetail.date!)[1]} ${CommonMethods.dateTimeAmPm(date: feedbackDetail.date!)[2]}",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              )
            ],
          ),
          verticalSizedBox(10),

          ///Description
          SelectableText(
            "${feedbackDetail.details}",
            style: AppTextStyle.heading3Regular(textColor: AppColors.appBlack),
          ),
          // Text(
          //   "${feedbackDetail.details}",
          //   style: AppTextStyle.heading3Regular(textColor: AppColors.appBlack),
          // ),

          ///Image and file
          feedbackDetail.feedbackFiles!.isEmpty
              ? const SizedBox()
              : Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: imageAndFile(
                      feedbackDetail: feedbackDetail,
                      showTitle: false,
                      feedbackItemBloc: feedbackItemBloc,
                      context: context),
                ),

          verticalSizedBox(20),

          ///Divider
          divider(),
        ],
      ),
    );
  }

//endregion

//region Image or file
  static Widget imageAndFile(
      {required BuildContext context,
      required FeedbackDetail feedbackDetail,
      required bool showTitle,
      required FeedbackItemBloc feedbackItemBloc}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // showTitle?Padding(
        //   padding: const EdgeInsets.only(bottom: 5),
        //   child: appText("Attachments",fontWeight:FontWeight.w400,fontSize:14,fontFamily:AppConstants.rRegular,
        //     maxLine: 1,
        //     color: AppColors.writingBlack,
        //   ),
        // ):const SizedBox() ,
        SizedBox(
          height: 105,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: feedbackDetail.feedbackFiles!.length,
            itemBuilder: (BuildContext, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                        onTap: () {
                          //If image file
                          if (CommonMethods.isImageFile(
                              fileName: feedbackDetail
                                      .feedbackFiles![index].attachments ??
                                  feedbackDetail
                                      .feedbackFiles![index].images!)) {
                            var screen = BuyerImagePreviewScreen(
                              productImage: [
                                '${feedbackDetail.feedbackFiles![index].attachments ?? feedbackDetail.feedbackFiles![index].images}'
                              ],
                            );
                            var route =
                                MaterialPageRoute(builder: (context) => screen);
                            Navigator.push(
                                AppConstants
                                    .userStoreCommonBottomNavigationContext,
                                route);
                            return;
                          }
                          //If Document file
                          if (CommonMethods.isDocumentFile(
                              fileName: feedbackDetail
                                      .feedbackFiles![index].attachments ??
                                  feedbackDetail
                                      .feedbackFiles![index].images!)) {
                            var screen = AppWebView(
                                url:
                                    '${AppConstants.googleDriveBaseUrl}${AppConstants.baseUrl}${feedbackDetail.feedbackFiles![index].attachments ?? feedbackDetail.feedbackFiles![index].images}');
                            var route =
                                MaterialPageRoute(builder: (context) => screen);
                            Navigator.push(
                                    AppConstants
                                        .userStoreCommonBottomNavigationContext,
                                    route)
                                .then((value) {
                              //getSellerSubOrder();
                              //print(value);
                            });
                            return;
                          }
                          //If Video file or any
                          var screen = AppWebView(
                              url:
                                  '${AppConstants.baseUrl}${feedbackDetail.feedbackFiles![index].attachments ?? feedbackDetail.feedbackFiles![index].images}');
                          var route =
                              MaterialPageRoute(builder: (context) => screen);
                          Navigator.push(
                                  AppConstants
                                      .userStoreCommonBottomNavigationContext,
                                  route)
                              .then((value) {
                            //getSellerSubOrder();
                            //print(value);
                          });
                          return;

                          // feedbackItemBloc.startDownload(documentUrl:feedbackDetail.feedbackFiles![index].attachments??feedbackDetail.feedbackFiles![index].images!);
                        },
                        child: CommonMethods.isImageFile(
                                fileName: feedbackDetail
                                        .feedbackFiles![index].attachments ??
                                    feedbackDetail
                                        .feedbackFiles![index].images!)
                            ? Container(
                                color: AppColors.textFieldFill1,
                                padding: const EdgeInsets.all(10),
                                height: 100,
                                width: 100,
                                child: extendedImage(
                                    imageHeight: 100,
                                    imageWidth: 100,
                                    feedbackDetail
                                        .feedbackFiles![index].images!,
                                    customPlaceHolder:
                                        AppImages.productPlaceHolder,
                                    context,
                                    200,
                                    200),
                              )
                            : Container(
                                color: AppColors.textFieldFill1,
                                height: 100,
                                width: 100,
                                padding: const EdgeInsets.all(10),
                                child: Image.asset(
                                    fit: BoxFit.cover,
                                    AppCommonWidgets.filePlaceHolder(
                                        filePath: feedbackDetail
                                                .feedbackFiles![index]
                                                .attachments ??
                                            feedbackDetail.feedbackFiles![index]
                                                .images!)),
                              )),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
//endregion
}
