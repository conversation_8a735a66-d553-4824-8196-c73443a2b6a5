import 'dart:io';

import 'package:flutter/material.dart';
import 'package:swadesic/features/support/feedback_item/feedback_item_bloc.dart';
import 'package:swadesic/features/support/feedback_item/feedback_item_common_widgets.dart';
import 'package:swadesic/features/support/support_common_widgets.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';

class FeedbackItemScreen extends StatefulWidget {
  final int feedbackId;
  final bool isAdmin;

  const FeedbackItemScreen(
      {Key? key, required this.feedbackId, required this.isAdmin})
      : super(key: key);

  @override
  State<FeedbackItemScreen> createState() => _FeedbackItemScreenState();
}

class _FeedbackItemScreenState extends State<FeedbackItemScreen> {
  //region Bloc
  late FeedbackItemBloc feedbackItemBloc;

  //endregion

  //region Bloc
  @override
  void initState() {
    feedbackItemBloc = FeedbackItemBloc(context, widget.feedbackId);
    feedbackItemBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
          appBar: appBar(),
          backgroundColor: AppColors.appWhite,
          resizeToAvoidBottomInset: true,
          body: body()),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        Navigator.pop(context);
        // widget.isFromBottomNavigation?supportBloc.goToFirstBottomNavigation():Navigator.pop(context);
      },
      context: context,
      isMembershipVisible: false,
      isCartVisible: false,
      isDefaultMenuVisible: false,
      title: AppStrings.feedbackItem,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return StreamBuilder<FeedbackItemState>(
        stream: feedbackItemBloc.feedbackItemCtrl.stream,
        initialData: FeedbackItemState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == FeedbackItemState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == FeedbackItemState.Success) {
            return rootFeedback();
          }
          return const SizedBox();
        });
  }

  //endregion

//region Root feedback
  Widget rootFeedback() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await feedbackItemBloc.init();
      },
      child: ListView(
        shrinkWrap: true,
        // mainAxisSize: MainAxisSize.min,
        children: [
          FeedbackItemCommonWidgets.rootFeedbackItemCard(
            feedbackDetail: feedbackItemBloc
                .getAllFeedbackResponse.feedbackDetailList!.first,
            onTapVote: () {
              feedbackItemBloc.onTapUpVote(
                  rootFeedback: feedbackItemBloc
                      .getAllFeedbackResponse.feedbackDetailList!.first);
            },
            isAdmin: widget.isAdmin,
            feedbackItemBloc: feedbackItemBloc,
            context: context,
          ),
          feedbackItemBloc.getAllFeedbackResponse.feedbackDetailList!.first
                      .feedbackReplayList ==
                  null
              ? const SizedBox()
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: feedbackItemBloc.getAllFeedbackResponse
                      .feedbackDetailList!.first.feedbackReplayList!.length,
                  itemBuilder: (context, index) {
                    // //print(widget.feedbackDetail.feedbackReplayList!.length);
                    // return Icon(Icons.add);
                    return FeedbackItemCommonWidgets.replayFeedbackItemCard(
                        feedbackDetail: feedbackItemBloc
                            .getAllFeedbackResponse
                            .feedbackDetailList!
                            .first
                            .feedbackReplayList![index],
                        feedbackItemBloc: feedbackItemBloc,
                        context: context);
                  }),

          ///Attachment and button
          addNewResponse(),

          //callNative(),
          AppCommonWidgets.bottomListSpace(context: context),
        ],
      ),
    );

    // return FeedbackItemCommonWidgets.rootFeedbackItemCard(feedbackDetail: widget.feedbackDetail, onTapVote: (){},);
    // return FeedbackItemCommonWidgets.replayFeedbackItemCard(feedbackDetail: widget.feedbackDetail);
  }

//endregion

//region Add new response
  Widget addNewResponse() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Visibility(
              visible: !feedbackItemBloc.isAddNewResponseVisible,
              child: InkWell(
                onTap: () {
                  feedbackItemBloc.onTapAddNewResponse();
                },
                child: Row(
                  children: [
                    Expanded(
                        child: Container(
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: AppColors.textFieldFill1,
                          boxShadow: AppColors.appShadow),
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        "Add new response",
                        style:
                            AppTextStyle.access0(textColor: AppColors.appBlack),
                      ),
                    )),
                  ],
                ),
              )),
          Visibility(
            visible: feedbackItemBloc.isAddNewResponseVisible,
            child: Column(
              children: [
                AppTitleAndOptions(
                  title: AppStrings.newResponse,
                  option: AppTextFields.allTextField(
                    context: context,
                    maxEntry: 1000,
                    minLines: 5,
                    maxLines: 5,
                    textEditingController: feedbackItemBloc.detailTextCtrl,
                    hintText: AppStrings.addMoreDetail,
                  ),
                ),

                // Padding(
                //   padding: const EdgeInsets.symmetric(vertical: 10),
                //   child: Row(
                //     mainAxisSize: MainAxisSize.max,
                //     crossAxisAlignment: CrossAxisAlignment.center,
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     children: [FeedbackItemCommonWidgets.title(title: "New response"), SvgPicture.asset(AppImages.exclamation)],
                //   ),
                // ),
                // colorFilledTextField(
                //   context: context,
                //   textFieldCtrl: feedbackItemBloc.detailTextCtrl,
                //   hintText: "Add more details",
                //   textFieldMaxLine: 5,
                //   minLines: 5,
                //   maxCharacter: 100,
                //   keyboardType: TextInputType.text,
                //   textInputAction: TextInputAction.next,
                //   regExp: AppConstants.acceptAll,
                //   fieldTextCapitalization: TextCapitalization.sentences,
                //   hintFontSize: 12,
                //   hintFontFamily: AppConstants.rRegular,
                //   hintTextColor: AppColors.writingColor3,
                // ),

                InkWell(
                  onTap: () {
                    feedbackItemBloc.onTapAddFile();
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 20),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15, vertical: 10),
                    decoration: const BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                    ),
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppImages.plus,
                          color: AppColors.appBlack,
                        ),
                        horizontalSizedBox(10),
                        Text(
                          "Upload screenshots, images, videos or docs",
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                      ],
                    ),
                  ),
                ),
                feedbackItemBloc.files.isEmpty
                    ? const SizedBox()
                    : Row(
                        children: [
                          Expanded(
                            child: SizedBox(
                              height: 100,
                              child: ListView.builder(
                                  shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemCount: feedbackItemBloc.files.length,
                                  itemBuilder: (context, index) {
                                    return Stack(
                                      alignment: Alignment.topRight,
                                      children: [
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 10),
                                          child: imageAndDocument(
                                              data: feedbackItemBloc
                                                  .files[index]),
                                        ),
                                        Positioned(
                                            top: 2,
                                            right: 2,
                                            child: InkWell(
                                                onTap: () {
                                                  //print(index);
                                                  feedbackItemBloc
                                                      .onTapRemoveFile(
                                                          data: feedbackItemBloc
                                                              .files[index]);
                                                },
                                                child: SvgPicture.asset(
                                                    AppImages.removeCircle)))
                                      ],
                                    );
                                  }),
                            ),
                          ),
                          const SizedBox()
                        ],
                      ),
                subMit()
              ],
            ),
          ),
        ],
      ),
    );
  }

//endregion

  //region Image and document
  Widget imageAndDocument({required File data}) {
    ///Split file name to identify the extension
    String? fileType;
    fileType = CommonMethods.returnExtension(file: data);
    if (feedbackItemBloc.otherFileTypeList.contains(fileType)) {
      return Container(
          width: 100,
          height: 100,
          color: AppColors.textFieldFill1,
          child: const Icon(Icons.file_copy));
    }
    return SizedBox(
        width: 100,
        height: 100,
        child: Image.file(
          data,
          cacheHeight: 800,
          cacheWidth: 800,
          fit: BoxFit.cover,
        ));
  }

  //endregion

//region Submit button
  Widget subMit() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: SupportScreenCommonWidgets.subMitButton(
          buttonName: "Submit",
          onPressed: () {
            feedbackItemBloc.addFeedbackReplay();
          }),
    );
    // return Padding(
    //   padding: const EdgeInsets.symmetric(vertical: 20),
    //   child: FeedbackItemCommonWidgets.subMitButton(
    //       buttonName: "Submit",
    //       onPressed: () {
    //         feedbackItemBloc.addFeedbackReplay();
    //       }),
    // );
  }
//return SupportScreenCommonWidgets.subMitButton(buttonName:"Submit", onPressed:(){
//       giveFeedbackBloc.addFeedback();
//     });
//endregion

//region Call
  Widget callNative() {
    return CupertinoButton(
        child: const Text("call native"),
        onPressed: () {
          // feedbackItemBloc.callNative();
        });
  }
//endregion
}
