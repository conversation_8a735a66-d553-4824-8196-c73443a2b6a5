import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/buyer_and_seller_order_card/buyer_and_seller_order_card.dart';
import 'package:swadesic/features/seller/seller_all_orders/all_order_filter/all_order_filter.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_orders_pagination.dart';
import 'package:swadesic/features/widgets/no_orders_yet/no_orders_yet.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Seller All Order Screen
class SellerAllOrdersScreen extends StatefulWidget {
  final int storeId;
  final bool? isFromBottomNavigation;
  final String? orderNumberFromNotification;

  const SellerAllOrdersScreen(
      {Key? key,
      required this.storeId,
      this.isFromBottomNavigation = false,
      this.orderNumberFromNotification})
      : super(key: key);

  @override
  _SellerAllOrdersScreenState createState() => _SellerAllOrdersScreenState();
}
// endregion

class _SellerAllOrdersScreenState extends State<SellerAllOrdersScreen> {
  // region Bloc
  late SellerAllOrdersBloc sellerAllOrdersBloc;
  late SellerOrdersPagination _pagination;

  // endregion

  // region Init & Dispose
  @override
  void initState() {
    super.initState();
    sellerAllOrdersBloc = SellerAllOrdersBloc(
        context, widget.storeId, widget.orderNumberFromNotification);
    _pagination = SellerOrdersPagination(context, sellerAllOrdersBloc);
    sellerAllOrdersBloc.init();
  }

  @override
  void dispose() {
    sellerAllOrdersBloc.dispose();
    _pagination.dispose();
    super.dispose();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body: GestureDetector(
          onTap: () {
            CommonMethods.closeKeyboard(context);
          },
          child: SafeArea(child: body())),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      onTapLeading: () {
        widget.isFromBottomNavigation!
            ? sellerAllOrdersBloc.onTapLeading()
            : Navigator.pop(context);
      },
      title: AppStrings.allOrders,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  // region Body
  Widget body() {
    return Column(
      children: [
        AllOrderFilter(
          sellerAllOrdersBloc: sellerAllOrdersBloc,
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 9),
            child: StreamBuilder<SellerAllOrderState>(
                stream: sellerAllOrdersBloc.sellerAllOrderCtrl.stream,
                initialData: SellerAllOrderState.Loading,
                builder: (orderContext, snapshot) {
                  if (snapshot.data == SellerAllOrderState.Loading) {
                    return Center(
                        child: AppCommonWidgets.appCircularProgress());
                  }
                  if (snapshot.data == SellerAllOrderState.Failed) {
                    return AppCommonWidgets.errorWidget(
                        errorMessage: AppStrings.unableToLoadOrders,
                        onTap: () async {
                          await sellerAllOrdersBloc.init();
                        });
                  }
                  if (snapshot.data == SellerAllOrderState.Empty) {
                    return RefreshIndicator(
                      color: AppColors.brandBlack,
                      onRefresh: () async {
                        await sellerAllOrdersBloc.init();
                      },
                      child: const NoOrdersYet(),
                    );
                    // return RefreshIndicator(
                    //   color: AppColors.brandBlack,
                    //   onRefresh: () async {
                    //     await sellerAllOrdersBloc.init();
                    //   },
                    //   child: ListView(
                    //     children: [
                    //       Container(
                    //           alignment: Alignment.center,
                    //           child: AppCommonWidgets.errorMessage(
                    //               error: AppStrings.noResultFound)),
                    //     ],
                    //   ),
                    // );
                    // return Center(
                    //   child: AppCommonWidgets.emptyResponseText(
                    //       emptyMessage: AppStrings.noResultFound),
                    // );
                  }
                  if (snapshot.data == SellerAllOrderState.Success) {
                    return order();
                  }
                  return const SizedBox();
                }),
          ),
        ),
      ],
    );
  }

// endregion

  //region Order
  Widget order() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await sellerAllOrdersBloc.init();
      },
      child: ListView.builder(
        controller: sellerAllOrdersBloc.scrollController,
        itemCount: sellerAllOrdersBloc.searchedOrders.length + 
                 (sellerAllOrdersBloc.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the bottom when loading more
          if (index >= sellerAllOrdersBloc.searchedOrders.length) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: AppCommonWidgets.appCircularProgress(),
              ),
            );
          }
          
          final order = sellerAllOrdersBloc.searchedOrders[index];
          return InkWell(
            onTap: () {
              sellerAllOrdersBloc.onTapOrderCard(order: order);
            },
            child: BuyerAndSellerOrderCard(
              order: order,
              buyerSellerOrderScreenContext: context,
              storeAndProfilePlaceHolder: AppImages.userPlaceHolder,
            ),
          );
        },
      ),
    );
  }

  //endregion
}
