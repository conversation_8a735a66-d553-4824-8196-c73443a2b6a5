import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/model/support/get_all_feedback_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class SupportScreenCommonWidgets {
  //region Title
  static Widget title({required String title}) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
        )
        // appText(title,fontWeight:FontWeight.w600,fontSize: 15,fontFamily:AppConstants.rRegular,
        //   maxLine: 5,
        //   color: AppColors.appBlack,
        //
        // ),
      ],
    );
  }
  //endregion

  //region Issue and suggestion Radio
  static Widget issueAndSuggestionRadio(
      {required String title,
      required String subTitle,
      required bool isActive,
      required onPress}) {
    return InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        decoration: BoxDecoration(
            border: Border.all(color: AppColors.borderColor1),
            borderRadius: BorderRadius.circular(50)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(isActive
                ? AppImages.circularRadioTrue
                : AppImages.circularRadioFalse),
            horizontalSizedBox(10),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack),
                  ),
                  Text(
                    subTitle,
                    maxLines: 2,
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
  //endregion

//region Green button
  static Widget greenButton({required String buttonName, required onPress}) {
    return InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppColors.inActiveGreen,
        ),
        child: Text(
          buttonName,
          style: AppTextStyle.button1Bold(textColor: AppColors.appWhite),
        ),
        // child:appText(buttonName,color:AppColors.brandGreen,fontSize: 15,
        //     fontFamily: AppConstants.rRegular,
        //     fontWeight: FontWeight.w700
        // )
      ),
    );
  }
//endregion

//region Submit button
  static Widget subMitButton({required String buttonName, required onPressed}) {
    return InkWell(
      onTap: () {
        onPressed();
      },
      child: Row(
        children: [
          Expanded(
              child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppColors.brandBlack),
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              buttonName,
              style: AppTextStyle.access0(textColor: AppColors.appWhite),
            ),
          )),
        ],
      ),
    );
  }
//endregion

//region All Feedback card
  static Widget allFeedbackCard(
      {required FeedbackDetail feedbackDetail,
      required onTapVote,
      required onTapRightArrow}) {
    return Container(
      padding: const EdgeInsets.only(top: 5),
      margin: const EdgeInsets.only(left: 10, right: 10, bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Feedback date time and status
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                //Feedback id and date time
                Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Ticket Id: #${feedbackDetail.feedbackId}",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                    verticalSizedBox(3),
                    Text(
                      "${CommonMethods.dateTimeAmPm(date: feedbackDetail.date!)[1]} ${CommonMethods.dateTimeAmPm(date: feedbackDetail.date!)[2]}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack1),
                    )
                  ],
                ),
                Expanded(child: horizontalSizedBox(10)),
                //Status
                // Container(
                //   alignment: Alignment.center,
                //   padding:
                //       const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                //   decoration: BoxDecoration(
                //     color: AppColors.brandBlack.withOpacity(0.2),
                //     border: Border.all(color: AppColors.brandBlack),
                //     borderRadius: BorderRadius.circular(20),
                //   ),
                //   child: Text(
                //     feedbackDetail.status!,
                //     style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                //   ),
                // )
              ],
            ),
          ),
          verticalSizedBox(10),

          ///Title, Desc and right arrow
          Padding(
            padding: const EdgeInsets.only(left: 10),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      //Title
                      Text(
                        "${feedbackDetail.brief}",
                        maxLines: 2,
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack),
                      ),

                      verticalSizedBox(9),
                      //Desc
                      Text(
                        "${feedbackDetail.details}",
                        maxLines: 1,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack),
                      ),

                      // appText("${feedbackDetail.details}",
                      //   fontWeight:FontWeight.w400,fontSize: 13,fontFamily:AppConstants.rRegular,
                      //   maxLine: 1,
                      //   color: AppColors.writingColor2,
                      //
                      // ),
                    ],
                  ),
                ),
                // horizontalSizedBox(20),
                // const Icon(Icons.keyboard_arrow_right,color: AppColors.appBlack,)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  height: 30,
                  width: 30,
                  child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      onTapRightArrow();
                    },
                    child: RotatedBox(
                        quarterTurns: 3,
                        child: SvgPicture.asset(AppImages.downArrow2)),
                  ),
                )
                // SvgPicture.asset(AppImages.arrow3)
              ],
            ),
          ),
          verticalSizedBox(10),

          ///Category
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Text(
              "Category: ${feedbackDetail.screenCategory ?? 'none'}",
              style:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            ),
          ),
          verticalSizedBox(10),

          ///Feedback type and vote
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ///report or suggestion
                Container(
                  alignment: Alignment.center,
                  padding:
                      const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                  decoration: BoxDecoration(
                    color: feedbackDetail.feedbackType == "REPORT"
                        ? AppColors.red.withOpacity(0.2)
                        : feedbackDetail.feedbackType == "ESCALATION"
                            ? AppColors.orange.withOpacity(0.2)
                            : AppColors.darkPurple.withOpacity(0.2),
                    border: Border.all(
                        color: feedbackDetail.feedbackType == "REPORT"
                            ? AppColors.red
                            : feedbackDetail.feedbackType == "ESCALATION"
                                ? AppColors.orange
                                : AppColors.darkPurple),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    feedbackDetail.feedbackType!,
                    style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                  ),
                ),

                ///Vote arrow
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  width: 30,
                  height: 30,
                  child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        onTapVote();
                      },
                      child: SvgPicture.asset(feedbackDetail.votedUserList!
                              .contains(AppConstants.appData.isStoreView!
                                  ? AppConstants.appData.storeReference
                                  : BuyerHomeBloc.userDetailsResponse
                                      .userDetail!.userReference!)
                          ? AppImages.arrowActive
                          : AppImages.arrowInActive)),
                ),

                ///Support count
                Text(
                  feedbackDetail.upvoteCount.toString(),
                  style: AppTextStyle.smallText(textColor: AppColors.appBlack),
                ),
              ],
            ),
          ),
          verticalSizedBox(20),

          ///Divider
          divider(),
        ],
      ),
    );
  }
//endregion
}
