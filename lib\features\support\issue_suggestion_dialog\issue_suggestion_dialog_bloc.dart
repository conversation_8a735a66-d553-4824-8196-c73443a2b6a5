import 'package:flutter/material.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/util/app_constants.dart';


class IssueSuggestionDialogBloc {
  // region Common Variables
  late BuildContext context;
   bool isReport = true;


  // endregion

  //region Text controller

  //endregion

  //region Controller
  //endregion

  // region | Constructor |
  IssueSuggestionDialogBloc(this.context);

  // endregion

  // region Init
  void init(){

  }
// endregion

  //region On Tap Go to support
  onTapGoToSupport(){
    Navigator.pop(context);

    // Create appropriate support screen based on user/store context
    // Both users and stores accessing general support should not have targetStoreReference
    // This ensures they get their respective appropriate support sections:
    // - Users: Create ticket, My tickets (general support)
    // - Stores: Tickets to me, My tickets, Create ticket (own store support)
    var screen = SupportScreen(
      isReport: isReport,
      // No target store reference - this is general support access
      // The support screen will determine the appropriate tabs based on AppConstants.appData context
    );

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }
//endregion


}
