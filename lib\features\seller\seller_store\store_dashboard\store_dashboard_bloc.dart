import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/recently_visited_store/recently_visited_store_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_with_appBar.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_bottom_sheet_screen/share_store_bottom_sheet_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/add_product/add_product_screen.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_screen.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/features/seller/seller_dashboard_and_rewards/seller_dashboard_and_rewards_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/seller_account_balance_and_rewards.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_settings/seller_store_settings_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/wating_orders_dialog/waiting_orders_dialog.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

enum StoreDashBoardState { Loading, Success, Failed, Empty }
enum PinCodeState { Loading, Success, Failed, Empty }

class StoreDashBoardBloc {
  // region Common Variables
  BuildContext context;
  // bool openForOrder = false;
  final String storeReference;
  ///Region store dashboard
  // late StoreDashboardService storeDashboardService;
  // late StoreDashboardResponse storeDashboardResponse;
  ///Single store info
  // static late SingleStoreInfoResponse singleStoreInfoResponse;
  // late SingleStoreInfoServices singleStoreInfoServices;
  //Notification data model
  late UserOrStoreNotificationDataModel notificationDataModel;
  String productDeepLink = '';
  ///Cache service
  late CacheStorageService cacheStorageService;
  // Get reference to the StoreDashboardDataModel
  late StoreDashboardDataModel storeDashboardDataModel;
  GlobalKey orderCardKey = GlobalKey();

  // endregion

  //region Controller
  final storeDashboardCtrl = StreamController<StoreDashBoardState>.broadcast();
  final storeOnlineCtrl = StreamController<bool>.broadcast();
  static  StreamController<PinCodeState> pinCodeStateCtrl = StreamController<PinCodeState>.broadcast();
  //endregion

  // region | Constructor |
  StoreDashBoardBloc(this.context,this.storeReference);

  // endregion

  // region Init
 Future<void> init() async{
   ///Save Dashboard context
    AppConstants.currentSelectedTabContext = context;
    ///Single store service initialize
    // singleStoreInfoServices = SingleStoreInfoServices();
    ///Get store info
    // getSingleStoreInfo();
    //getStoreDashboard();
    ///Cache initialize
    cacheStorageService = CacheStorageService();
    ///Save store reference to share pref
    saveStoreReference();
    /// Retrieve to the StoreDashboardDataModel
    storeDashboardDataModel = Provider.of<StoreDashboardDataModel>(context, listen: false);
    //Get store conf
    //getStoreConfig();



  }
// endregion

  //region On Profile
  void onTapProfile(){

  }
  //endregion

  //region On Tap Store
  void onTapStore({required String data}){
    //Add global selected store reference
    AppConstants.appData.storeReference = data;

  }
  //endregion
///not in use
  //region Get Store Info Api call
  // getSingleStoreInfo() async {
  //   try {
  //     //Loading
  //     // storeDashboardCtrl.sink.add(StoreDashBoardState.Loading);
  //     singleStoreInfoResponse = await singleStoreInfoServices.getSingleStoreInfo(storeReference);
  //     //Get store dashboard info
  //     getStoreDashboard();
  //   } on ApiErrorResponseMessage {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     storeDashboardCtrl.sink.add(StoreDashBoardState.Failed);
  //     return;      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //   } catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
  //     storeDashboardCtrl.sink.add(StoreDashBoardState.Failed);
  //     return;      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //   }
  // }
//endregion





  //region Open close store
  storeOpenClose()async{
    try{
      //On change store status
      storeDashboardDataModel.storeDashBoard.openForOrder = !storeDashboardDataModel.storeDashBoard.openForOrder!;
      //Showing message
      CommonMethods.toastMessage(storeDashboardDataModel.storeDashBoard.openForOrder!?AppStrings.yourStoreIsCurrentlyOpen:AppStrings.yourStoreIsCurrentlyClosed, context);
      //Refresh ui
      storeOnlineCtrl.sink.add(true);
      storeDashboardCtrl.sink.add(StoreDashBoardState.Success);
      await StoreDashboardService().openCloseStore(storeReference:storeReference);
    }
    on ApiErrorResponseMessage catch(error){
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    }
    catch(error){
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
  //endregion

  //region Store online and offline
    void storeOnlineOffline(bool storeDate) {
      storeDashboardDataModel.storeDashBoard.openForOrder = storeDate;
      //Update ui
      storeDashboardDataModel.updateUi();
      storeOnlineCtrl.sink.add(storeDashboardDataModel.storeDashBoard.openForOrder!);
      storeOpenClose();
    }
  //endregion

  //region On tap checklist drop down
  void onTapCheckListDropDown(){

  }
  //endregion

  //region Get store dashboard
  Future<void>getStoreDashboard()async{
    try{
      //Api call
      StoreDashboardResponse storeDashboardResponse = await StoreDashboardService().getStoreDashboard(storeReference:storeReference);

      ///Set data into data model
      storeDashboardDataModel.addDashboard(data: storeDashboardResponse.data!);
     //Store open or close
     //  openForOrder = storeDashboardDataModel.storeDashBoard.openForOrder!;
      storeOnlineCtrl.sink.add(storeDashboardDataModel.storeDashBoard.openForOrder!);
      //Percentage calculate
      // calculatePercentage();
      //Get store status config
      getStoreConfig();
      storeDashboardCtrl.sink.add(StoreDashBoardState.Success);
    }
    on ApiErrorResponseMessage {
      storeDashboardCtrl.sink.add(StoreDashBoardState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
    catch(error){
      storeDashboardCtrl.sink.add(StoreDashBoardState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
  //endregion

  //region Active and de active api call
  Future<void>activeDeActiveApiCall()async{
   // ///Check is add product
   //  if(!storeDashboardDataModel.storeDashBoard.addProducts!){
   //    return CommonMethods.toastMessage(AppStrings.pleaseAddAtLeast3Products, context);
   //  }
   //
   //  ///Check is trust center is added
   //  if(!storeDashboardResponse.data!.addProducts!){
   //    return CommonMethods.toastMessage(AppStrings.pleaseCompleteTrustCenterDetails, context);
   //  }
   ///Check is trust center is not added
    if(!storeDashboardDataModel.storeDashBoard.trustcenterDetail!){
      return CommonMethods.toastMessage(AppStrings.pleaseCompleteTrustCenterDetails, context);
    }
    try{
      //Go to congratulation screen
      //Open congratulation dialog
      if(context.mounted){
        CommonMethods.showCelebration(context: context,isPlay: true, celebrationMessage: AppStrings.congratulationYourStoreIsLiveNow);
      }
      //Api call for open/close for order
      await StoreDashboardService().activeAndDeActiveStore(storeReference:storeReference,storeActiveStatus:!storeDashboardDataModel.storeDashBoard.isActive! );
      //Update data
      storeDashboardDataModel.storeDashBoard.isActive = true;
      //Update ui
      storeDashboardDataModel.updateUi();

      //Init
      init();
    }
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      storeDashboardCtrl.sink.add(StoreDashBoardState.Failed);
      return;
    }
    catch(error){
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      storeDashboardCtrl.sink.add(StoreDashBoardState.Failed);
      return;
    }
  }
  //endregion

  //region Get store config
  void getStoreConfig()async{
    // Get reference to the store conf
    var storeConfigDataModel = Provider.of<StoreConfigDataModel>(context, listen: false);

    try{
      //Api call for open/close for order
      var data = await StoreDashboardService().getStoreConfig(storeReference:storeReference);

      //Add data to data model
      storeConfigDataModel.addStoreConfig(data: data);
      //Update ui
      storeConfigDataModel.updateUi();

    }
    on ApiErrorResponseMessage catch(e) {

      return;
    }
    catch(error){
      return;
    }
  }
  //endregion




  //region Go to Seller All Order
  void goToSellerAllOrder() {
    var screen = SellerAllOrdersScreen(
      storeId:AppConstants.appData.storeId!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      getStoreDashboard();
    });
  }
//endregion

  //region Go to Add Product
    void goToAddProduct() {

      var screen = AddProductScreen(
        storeId:AppConstants.appData.storeId!, storeReference:AppConstants.appData.storeReference!,

      );
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route).then((value) {
        getStoreDashboard();
      });

    //If both delivery and return settings are there
    //   if(storeDashboardResponse.data!.warrantyAndReturn! && storeDashboardResponse.data!.warrantyAndReturn!){
    //     var screen = AddProductScreen(
    //       storeId:singleStoreInfoResponse.data!.storeid!, storeReference:singleStoreInfoResponse.data!.storeReference!,
    //
    //     );
    //     var route = MaterialPageRoute(builder: (context) => screen);
    //     Navigator.push(context, route);
    //   }
    //   else{
    //     CommonMethods.toastMessage(AppStrings.pleaseAddDeliveryAndReturn, context);
    //   }
      }


  //endregion


  //region On tao questions
  void onTapQuestions(){
    var screen =  const AppWebView(url: "https://github.com/error404sushant/public_json/blob/main/demo.md");
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
//endregion

  //region Go to Seller Settings Screen
  void goToSellerSettings() {
    var screen = SellerStoreSettingsScreen(
      storeRef: AppConstants.appData.storeReference!, storeId:AppConstants.appData.storeId!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to edit product
  void goToEditProduct() {
    var screen = EditProductScreen(storeId:AppConstants.appData.storeId!, storeRef:AppConstants.appData.storeReference!,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      getStoreDashboard();

    });
  }
//endregion

  //region On Tap Find your friends
  goToFindYourCustomer() {
    var screen =  FindYourCustomersScreen( visibleNext: false, title: AppStrings.findYourCustomersOnSwadesic,);
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion
  //region On Tap Favourite store
  onTapFavouriteStore() {

    var screen = const SupportedStoresScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On Tap recently visited store
  onTapRecentlyVisitedStore() {
    //RecentlyVisitedStoreScreen

    var screen = const RecentlyVisitedStoreScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On Tap Go to support
  onTapGoToSupport({required bool isReport, String? targetStoreReference}){
    var screen = SupportScreen(isReport:isReport, targetStoreReference: targetStoreReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
//endregion

  //region Go to Account balance
  void goToAccountBalance() {
    var screen = SellerAccountBalanceAndRewards(
      storeReference: AppConstants.appData.storeReference!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      getStoreDashboard();

    });
  }
//endregion


  //region Go to Trust center
  void goToTrustCenter(){
    var screen = SellerTrustCenterScreen(storeRef: AppConstants.appData.storeReference!,isStoreOwnerView: true,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //getStoreDashboard();

    });
  }
  //endregion


  //region On tap share store
  void onTapShareStore({required StoreInfo storeInfo}) async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(10), topLeft: Radius.circular(10))),
        builder: (context) {
          return  SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: ShareInSocialBottomSheetScreen(storeInfo: storeInfo));
        }).then((value) {
    });
  }
//endregion


  //region Open facebook
  void openFacebook({required StoreInfo storeInfo}) async {
    String shareLink = AppConstants.domainName + storeInfo.storehandle!;

    //print("https://www.facebook.com/sharer/sharer.php?u=$shareLink&t=");
    await launch("fb://feed");
  }

//endregion

  //region Open Whats App
    void openWhatsapp({required StoreInfo storeInfo}) async {
      String shareLink = AppConstants.domainName + storeInfo.storehandle!;
      String url = "https://wa.me/?text=$shareLink";
      await launch(url);
    }
  //endregion


  //region Save store reference
  void saveStoreReference()async{
    await cacheStorageService.saveString(StorageKeys.storeReferenceKey,storeReference);
  }
  //endregion


  //region Go to Seller Accounts Screen
  void goToSellerAccountScreen() {
    var screen = SellerAccountsScreen(userReference: AppConstants.appData.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion



  //region Go to Invitees
  void goToInvitees() {
    var screen = SellerAccountBalanceAndRewards(storeReference: AppConstants.appData.storeReference!,selectedTab: 1,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion
  //region Go to Seller rewards
  void goToSellerRewards() {
    var screen = SellerDashboardAndRewards();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion


  //region Go to affiliate program
  void goToAffiliateProgram(){
    var  screen =  const AffiliateProgramWithAppBar();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

//region Dispose
void dispose(){
  storeDashboardCtrl.close();
  storeOnlineCtrl.close();
}
//endregion

}
