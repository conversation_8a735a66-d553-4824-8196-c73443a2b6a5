import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_with_appBar.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/seller/deactivate_and_delete_store/deactivate_and_delete_store_screen.dart';
import 'package:swadesic/features/seller/my_plan/my_plan_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivey_setting_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_profile/seller_store_profile_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/app_and_security/app_and_security_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/app_and_security/app_font_change/app_font_change.dart';
import 'package:swadesic/model/app_settings/app_settings.dart';
import 'package:swadesic/subscription/subscription_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
enum SellerSettingState { Success, Empty }

class SellerStoreSettingsBloc {
  // region Common Variables
  BuildContext context;
  List<AppSetting> sellerAppSettingList = [];
  List<AppSetting> searchedSettingList = [];
  final String storeRef;
  final int storeId;

  // endregion

  //region Text editing controller
  final TextEditingController searchTextCtrl = TextEditingController();
  //endregion

  //region Controller
  final refreshCtrl = StreamController<SellerSettingState>.broadcast();
  //endregion

  // region | Constructor |
  SellerStoreSettingsBloc(this.context, this.storeRef, this.storeId);
  // endregion

  // region Init
  void init() {
    addSettingDataToList();




  }
// endregion


  //region Add setting data to list
  void addSettingDataToList(){

    //Add all setting in a list
    sellerAppSettingList= [
      //Store profile
      AppSetting(settingName:"Store profile",onTap: (){
       var  screen =  SellerStoreProfileScreen(storeReference: storeRef,);
       var route = CupertinoPageRoute(builder: (context) => screen);
       Navigator.push(context, route);
      } ),
      //My plan
      AppSetting(settingName:AppStrings.myPlan,onTap: (){
       var  screen =  MyPlanScreen();
       var route = CupertinoPageRoute(builder: (context) => screen);
       Navigator.push(context, route);
      } ),

      // ///Affiliate program
      // AppSetting(settingName:AppStrings.affiliateProgram,onTap: (){
      //   var  screen =  const AffiliateProgramWithAppBar();
      //   var route = CupertinoPageRoute(builder: (context) => screen);
      //   Navigator.push(context, route);
      // } ),
      //
      //Manage store subscription
      AppSetting(settingName:AppStrings.manageStoreSubscription,onTap: (){
        var  screen =  SubscriptionScreen();
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),

      //Trust center
      AppSetting(settingName:"Trust center",onTap: (){
        var  screen =  SellerTrustCenterScreen(storeRef: storeRef,isStoreOwnerView: true,);
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),


      //Delivery
      AppSetting(settingName:AppStrings.deliverySettings,onTap: (){
        //Check is store has completed the trust center
        // if(!StoreDashBoardBloc.storeDashboardResponse.data!.trustcenterDetail!){
        //   return CommonMethods.toastMessage(AppStrings.pleaseCompleteTrustCenterDetail, context);
        // }
        var screen = SellerStoreDeliverySettingScreen(
          storeRef: storeRef,
          isFromAddProduct: false,
          isFromStore: true,
          isFromEditProduct: false,
        );
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),

      //Return and refund settings
      AppSetting(settingName:AppStrings.returnAndRefundSettings,onTap: (){
        var  screen =  SellerReturnStoreWarrantyScreen(storeRef: storeRef,fromStoreScreen: true,);
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),

      //Support & suggestions
      AppSetting(settingName:"Support & suggestions",onTap: (){
        onTapGoToSupport(storeRef: storeRef);
      } ),

      //App font
      AppSetting(settingName: AppStrings.appFontSize,onTap: (){
        var  screen =  const AppFontChangeScreen();
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),


      //Deactivate or delete
      AppSetting(settingName: "Deactivate or delete my store",onTap: (){
        var  screen =  DeactivateAndDeleteStoreScreen(storeReference:storeRef,);
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),

      // //Privacy and policy
      // AppSetting(settingName: AppStrings.privacyAgreement,onTap: (){
      //   var  screen =  const AppWebView(url: AppConstants.appPrivacyAndPolicy);
      //   var route = CupertinoPageRoute(builder: (context) => screen);
      //   Navigator.push(context, route);
      // } ),
      //
      // //Terms and condition
      // AppSetting(settingName: AppStrings.termsAndCondition,onTap: (){
      //   var  screen =  const AppWebView(url: AppConstants.appTermsAndConditionSeller);
      //   var route = CupertinoPageRoute(builder: (context) => screen);
      //   Navigator.push(context, route);
      // } ),

      // region Go to App and security
      AppSetting(settingName: AppStrings.appAndSecurity,onTap: (){
        var screen =  const AppAndSecurityScreen();
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      } ),





//endregion


    ];

    //Add all setting list into searched list
    searchedSettingList.addAll(sellerAppSettingList);


  }
  //endregion



  //region On search
  void onSearch(){
    //Clear searched list
    searchedSettingList.clear();
    //If empty then all setting list in to searched list
    if(searchTextCtrl.text.isEmpty){
      searchedSettingList.addAll(sellerAppSettingList);
    }
    //If not empty then apply filter
    if(searchTextCtrl.text.isNotEmpty){
      searchedSettingList = sellerAppSettingList.where((element) => element.settingName.toLowerCase().contains(searchTextCtrl.text.toLowerCase())).toList();
    }
    //If searched list is empty then return stream empty status
    if(searchedSettingList.isEmpty){
      return refreshCtrl.sink.add(SellerSettingState.Empty);
    }
    //refresh ui
    refreshCtrl.sink.add(SellerSettingState.Success);
  }
  //endregion

  //region On Tap Go to support
  onTapGoToSupport({required String storeRef}){
    var screen = SupportScreen(
      isReport: true,
      targetStoreReference: storeRef,
      // No target store reference since this is general support from seller settings
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);

  }
//endregion
// region Go to muy plan
  goToMyPlan(){
    var screen =const MyPlanScreen();
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);

  }
//endregion

//region Dispose
void dispose(){
    refreshCtrl.close();
}
//endregion




}
