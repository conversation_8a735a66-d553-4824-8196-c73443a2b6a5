//happy
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/model/support/create_support_response.dart';
import 'package:swadesic/model/support/get_all_feedback_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class AddSupportServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  AddSupportServices() {
    httpService = HttpService();
  }

  // endregion


  // region Add support
  Future<CreateSupportResponse>addSupport({required String screenCategory,required String title,required String detail,required String feedbackType,required BuildContext context, String? targetReference, String? group}) async {
    //Retrieve data in Seller own store data model
    var sellerOwnStoreInfoDataModel = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
    var loggedInUserInfoDataModel =  Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

    // get body [for POST request]
    var body ={
        "user_reference": AppConstants.appData.isStoreView!
            ?AppConstants.appData.storeReference!
            :loggedInUserInfoDataModel.userDetail!.userReference!,
        // "name":AppConstants.appData.isStoreView!?
        // StoreDashBoardBloc.singleStoreInfoResponse.data!.storeName!
        //     :BuyerHomeBloc.userDetailsResponse.userDetail!.userName!,
      "name":AppConstants.appData.isStoreView!?
      sellerOwnStoreInfoDataModel.storeInfo!.storehandle
          :loggedInUserInfoDataModel.userDetail!.userName!,
        "screen_category": screenCategory,
        "brief": title,
        "details": detail,
      "feedback_type":feedbackType,
      "group": group
    };

    // Add target_reference if provided
    if (targetReference != null && targetReference.isNotEmpty) {
      body["target_reference"] = targetReference;
    }
    //print(body);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.getAddAndReplayFeedback);
    // return response;

    return CreateSupportResponse.fromJson(response);
  }
// endregion


  // region Replay support
  Future<CreateSupportResponse>replaySupport({required String details,required int feedbackId,required String userName}) async {
    // get body [for POST request]
    var body ={
      "user_reference": AppConstants.appData.userReference,
      "details": details,
      "name":userName,
      "feedback_type":AppConstants.appData.isStoreView! ? "STORE_RESPONSE" : "USER_RESPONSE",
      "feedback_id":feedbackId
    };
    //print(body);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(body,AppConstants.getAddAndReplayFeedback);
    // return response;
    return CreateSupportResponse.fromJson(response);

  }
// endregion


  // region Add vote
  addVote({required int feedbackId}) async {
    // get body [for POST request]
    var body ={
        "voter_reference":AppConstants.appData.isUserView!?AppConstants.appData.userReference:AppConstants.appData.storeReference
    };
    //print(body);
    var url = "${AppConstants.feedbackVote}$feedbackId/";
    //print(url);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.patchApi(body,url);
    // return response;

    // return CreateSupportResponse.fromJson(response);
  }
// endregion

  // region Update feedback status
  updateFeedbackStatus({required int feedbackId,required String status}) async {
    // get body [for POST request]
    var body ={
      "status": status
    };
    //print(body);
    var url = "${AppConstants.updateFeedback}$feedbackId/";
    //print(url);
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.putApiCall(body,url);
    // return response;

    // return CreateSupportResponse.fromJson(response);
  }
// endregion

  // region Get all feedback
  Future<GetAllFeedbackResponse>getAllFeedback() async {
    // get body [for POST request]
    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.getApiCall(AppConstants.getAddAndReplayFeedback);
    // return response;

    return GetAllFeedbackResponse.fromJson(response);
  }
// endregion

  // region Get tickets by type (SENT/RECEIVED)
  Future<GetAllFeedbackResponse> getTicketsByType({
    required String type, // SENT or RECEIVED
    required String entityReference, // userReference or storeReference
  }) async {
    Map<String, dynamic> response;

    // Build URL with query parameters
    String url = AppConstants.getAddAndReplayFeedback;
    List<String> queryParams = [];

    queryParams.add("type=${Uri.encodeComponent(type)}");
    queryParams.add("entity_reference=${Uri.encodeComponent(entityReference)}");

    if (queryParams.isNotEmpty) {
      url += "?${queryParams.join('&')}";
    }

    //#region Region - Execute Request
    response = await httpService.getApiCall(url);

    return GetAllFeedbackResponse.fromJson(response);
  }
  // endregion



  // region Get feedback detail
  Future<GetAllFeedbackResponse>getFeedbackDetail({required int feedbackId}) async {
    // get body [for POST request]
    // endregion
    Map<String, dynamic> response;

    var url = "${AppConstants.getFeedbackDetail}$feedbackId/";
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    // return response;

    return GetAllFeedbackResponse.fromJson(response);
  }
// endregion




}
